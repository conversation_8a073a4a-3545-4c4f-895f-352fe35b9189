{"name": "emoji-extension", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "node scripts/build.js dev", "watch": "node scripts/watch.js", "build": "node scripts/build.js build", "build:prod": "node scripts/build.js build:prod", "build:no-indexeddb": "node scripts/build.js build:no-indexeddb", "build:minimal": "node scripts/build.js build:minimal", "build:userscript": "node scripts/build.js build:userscript", "build:userscript:remote": "node scripts/build.js build:userscript remote", "build:userscript:min": "node scripts/build.js build:userscript:min", "build:debug": "node scripts/build.js build:debug", "serve": "vite preview", "test": "playwright test", "test:debug": "playwright test --debug", "test:extension": "playwright test --config=playwright.extension.config.ts", "test:extension:debug": "playwright test --config=playwright.extension.config.ts --debug", "release": "bash ./scripts/release.sh", "pack:crx": "node ./scripts/pack-crx.js", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx", "lint:fix": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit"}, "dependencies": {"ant-design-vue": "^4.1.2", "eslint": "^9.34.0", "globals": "^16.3.0", "pinia": "^2.1.7", "vue": "^3.4.21", "jszip": "^3.10.1"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@eslint/js": "^9.34.0", "@playwright/test": "^1.55.0", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.1.4", "@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/devtools": "^8.0.0", "autoprefixer": "^10.4.21", "crx": "^5.0.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-vue": "^10.4.0", "jsonc-eslint-parser": "^1.4.1", "less": "^4.4.1", "playwright": "^1.55.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^6.0.0", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.6"}, "devDependenciesExtra": {}, "dependenciesMeta": {}, "devDependenciesAdditions": {}, "devDependencies2": {}, "peerDependenciesMeta": {}, "bundledDependencies": [], "overrides": {}, "engines": {}}